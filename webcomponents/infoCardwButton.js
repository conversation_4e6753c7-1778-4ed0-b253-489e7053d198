import { html } from "lit-html";
import "./infoCardwButton.css";
import iconArrowRight from "../svg/icons/ArrowRight.svg";

export const infoCardwButton = ({
  icoPath,
  title,
  text,
  caption,
  linkText,
  background = "--info-card-background-blue",
}) => html`
  <div
    class="card info-card-w-button"
    style="background-color: var(${background});"
  >
    <div class="icon-container">
      ${icoPath ? html`<img src="${icoPath}" alt="Icon" />` : html``}
    </div>
    <div class="content-container">
      <div class="text-content">
        ${title ? html`<p class="title semibold">${title}</p>` : ""}
        ${text ? html`<p class="description small">${text}</p>` : ""}
        ${caption
          ? html`<p class="caption semibold"><strong>${caption}</strong></p>`
          : ""}
      </div>
      <div class="button-container">
        ${linkText
          ? html`
              <span class="link-text">${linkText}</span>
              <img
                class="img-button"
                src="${iconArrowRight}"
                alt="Pfeil nach rechts Icon"
              />
            `
          : html``}
        <!-- Wenn linkText leer, dann kein Pfeil rechts rendern -->
      </div>
    </div>
  </div>
`;
