import { html } from "lit-html";
import "./illustrationCard.css";
import iconArrowRight from "../svg/icons/ArrowRight.svg";

export const illustrationCard = ({
  bgColor,
  svgPath,
  title,
  text,
  showButton,
  buttonText,
}) => html`
  <div
    class="illustration-card"
    style="background-color: var(--${bgColor}, #ffffff);"
  >
    <div class="image-container">
      <div class="image">
        <img src="${svgPath}" alt="Illustration" />
      </div>
    </div>
    <div class="content-container">
      <h3 class="semibold">${title || "Titel"}</h3>
      <p class="small">${text || "Text"}</p>
      ${showButton
        ? html` <button class="text-icon-button">
            ${buttonText || "Weiter"}
            <img
              class="icon-right"
              src="${iconArrowRight}"
              alt="Pfeil nach rechts"
            />
          </button>`
        : ""}
    </div>
  </div>
`;
