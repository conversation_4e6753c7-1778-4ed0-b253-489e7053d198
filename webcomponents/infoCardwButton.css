/* Info Card */
.card.info-card-w-button {
  align-items: center;
  gap: 8px;
  flex-direction: row;
  width: 100%;
  color: var(--primary-brand);
}

.card.info-card-w-button p {
  margin: 0;
}

.card.info-card-w-button p.semibold {
  margin: 8px 0;
}

.card.info-card-w-button p.small {
  min-height: 24px;
  padding-top: 4px;
}

.card.info-card-w-button,
.card.info-card-w-button * {
  cursor: pointer;
}

.card.info-card-w-button > div {
  flex-grow: 1;
  align-content: flex-end;
  display: flex;
  flex-direction: column;
}

.card .img-button {
  color: var(--primary-brand);
  width: 24px;
  height: 24px;
  align-self: flex-end;
}

.card.info-card-w-button .button-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 8px;
}

.link-text {
  margin-right: 8px;
}

/* Pfeil Animation */
.card.info-card-w-button .img-button {
  transition: transform 0.3s ease;
}

.card.info-card-w-button:hover .img-button {
  transform: translateX(4px);
}
