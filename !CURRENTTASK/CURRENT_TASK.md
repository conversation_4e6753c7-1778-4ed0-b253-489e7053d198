# Aktuelle ToDos für Feature: [SettingsView]

## Allgemeine Hinweise für die KI

- Schreibe alle neuen Komponenten in lit-html.
- Speichere alle neuen Komponenten in /webcomponents
- Nutze bereits bestehende Komponenten bevor du neue Komponenten baust in /webcomponents
- Nutze camelCase für Variablen und Komponenten.
- Nutze kebabCase für CSS-Klassen.
- Nutze vorhandene CSS-Variablen von /variables.css
- Nutze die Schriftarten von /fonts.css
- Nutze die allgemeinen CSS Klassen von /styles.css und für Komponenten immer eine eigene CSS-Datei
- Berücksichtige bestehende Hilfsfunktionen aus /helpers
- Füge Kommentare hinzu, wenn Logik nicht selbsterklärend ist.
- Entferne ungenutzte Variablen und Imports aus der Seite auf der du gerade arbeitest
- Lies die /readme.md

---

## Aufgaben

1. [ ] Neue Seite anlegen: src/settings.js
2. [ ] Neue Seite im .bottomMenu verlinken
    2.1 [ ] Alte Route /profile im Router entfernen
    2.2 [ ] Neue Route /settings im Router anlegen
    2.3 [ ] Neue Route /settings im Router mit der neuen Seite verknüpfen
    2.4 [ ] Alte Verwendungen von /profile in der App durch /settings ersetzen
3. [ ] Neue Seite bearbeiten
    3.1 [ ] Ähnlichen Aufbau der Seite wie /src/deinBereich.js erstellen
    3.2 [ ] Headerbereich bauen unter #app via .page-content, .features-headline und .page-headline-container
    3.3 [ ] Wir benötigen keinen Segmented Control auf dieser Seite, dafür eine Liste mit den Einstellungen (Verwende die Komponente listItem aus /webcomponents/listItem.js)
    3.4 [ ] Die Einstellungen sind:
        - Push-Benachrichtigungen
        - Einwilligungen
        - Biometrie
        - Hilfe & Support
        - Nutzungsbedingungen
        - Datenschutz
        - Barrierefreinheit
        - Dein Profil
        - Deine NAVIDA-Daten
    3.5 [ ] Die Einstellungen sind alle Links zu Unterseiten, die wir später erstellen
    3.6 [ ] Füge darunter einen Textbutton ein mit dem icon "/images/icon_logout.svg" und dem Text "Abmelden"
    3.7 [ ] Füge zwischen dem Punkt "Biometrie" und "Hilfe & Support" eine blaue infoCard ein, dazu musst du eventuell die Liste in 2 Listen aufteilen
    3.8 [ ] Die blaue infoCard enthält den Text "Hilf uns in nur wenigen Minuten, die App für Dich zu verbessern." sowie einen Button "Jetzt Feedback geben" mit dem icon "/images/icon_linkExternal.svg"
4. [ ] Dokumentation für das neue Feature ergänzen
5. [ ] Code-Review und Refactoring
