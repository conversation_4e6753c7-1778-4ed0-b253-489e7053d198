import { html } from "lit-html";
import { pageHeadline } from "../webcomponents/pageHeadline.js";
import { BottomNavigation } from "./bottomNavigation.js";
import { sectionTitle } from "../webcomponents/sectionTitle.js";
import { featureCardSmall } from "../webcomponents/featureCardSmall.js";
import img_DoctorSearch from "../img/features/DoctorSearch.png";
import img_CheckUp from "../img/features/CheckUp.png";
import img_VideoSupport from "../img/features/VideoSupport.png";
import img_PhoneSupport from "../img/features/PhoneSupport.png";
import { featureCard } from "../webcomponents/featureCard.js";
import img_UserGoals from "../img/features/UserGoals.png";
import img_Compass from "../img/features/Compass.png";
import img_Events from "../img/features/Events.png";
import img_Magazine from "../img/features/Magazine.png";

/* Feature Cards Content Data Array */
const featureCardsSmallData = [
  {
    imgPath: img_DoctorSearch,
    title: "Arztsuche",
  },
  {
    imgPath: img_CheckUp,
    title: "Symptom&shy;check",
  },
  {
    imgPath: img_VideoSupport,
    title: "Videosprech&shy;stunde",
  },
  {
    imgPath: img_PhoneSupport,
    title: "Beratungs&shy;telefon",
  },
];
const featureCardsData = [
  {
    imgPath: img_UserGoals,
    title: "Deine Ziele",
    text: "Lege Ziele für Deine Gesundheit fest und lass Dich von mir unterstützen.",
    url: "/healthgoals-overview-no-goal",
  },
  {
    imgPath: img_Compass,
    title: "Vorsorgekompass",
    text: "Behalte Deine Vorsorge mit mir immer im Blick.",
    url: "/healthgoals-overview-no-goal",
  },
  {
    imgPath: img_Events,
    title: "Veranstaltungen",
    text: "Finde persönliche Veranstaltungen in Deiner Nähe und die zu Dir passen.",
    url: "/healthgoals-overview-no-goal",
  },
  {
    imgPath: img_Magazine,
    title: "Magazin",
    text: "Personalisierte Gesundheitsartikel, die zu Dir passen und Dich informieren.",
    url: "/healthgoals-overview-no-goal",
  },
];

/* Single Template that will combined at the bottom */
/* Page Title container and headline */
export const templateHeadline = () => html`
  <div class="features-headline">
    ${pageHeadline({
      bgColor: "primary-brand",
      title: "Funktionen",
      title2: "",
    })}
  </div>
`;

/* Section Title */
export const templateSectionTitle1 = () => html`
  ${sectionTitle("Gesund werden")}
`;

/* Template für alle Feature Cards */
const templateFeatureCardsSmall = () => html`
  ${featureCardsSmallData.map((card) =>
    featureCardSmall({
      imgPath: card.imgPath,
      title: card.title,
    })
  )}
`;
const templateFeatureCards = () => html`
  ${featureCardsData.map((card) =>
    featureCard({
      imgPath: card.imgPath,
      title: card.title,
      text: card.text,
      url: card.url,
    })
  )}
`;

/* Section Title */
export const templateSectionTitle2 = () => html`
  ${sectionTitle("Gesund bleiben")}
`;

export const featuresTemplate = () => html`
  ${templateHeadline()} ${templateSectionTitle1()}
  <div class="feature-cards-small-container">
    ${templateFeatureCardsSmall()}
  </div>
  ${templateSectionTitle2()}
  <div class="feature-cards-container last-container">${templateFeatureCards()}</div>

  ${BottomNavigation("home")}
`;
