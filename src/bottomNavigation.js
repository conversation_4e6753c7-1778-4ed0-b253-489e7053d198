import { html } from "lit-html";
import home from "../svg/icons/icon_home.svg";
import features from "../svg/icons/icon_features.svg";
import user from "../svg/icons/icon_user.svg";
import settings from "../svg/icons/icon_settings.svg";

export const BottomNavigation = (activeItem) => html`
  <div class="bottom-menu" id="bottomMenu">
    <a
      class="menu-item ${activeItem === "home" ? "active" : ""}"
      data-navigate="/homescreen"
    >
      <img src="${home}" alt="Start" />
      <span>Start</span>
    </a>
    <a
      class="menu-item ${activeItem === "features" ? "active" : ""}"
      data-navigate="/features"
    >
      <img src="${features}" alt="Funktionen" />
      <span>Funktionen</span>
    </a>
    <a
      class="menu-item ${activeItem === "settings" ? "active" : ""}"
      data-navigate="/settings"
    >
      <img src="${user}" alt="Dein Be<PERSON>" />
      <span><PERSON><PERSON></span>
    </a>
    <a
      class="menu-item ${activeItem === "profile" ? "active" : ""}"
      data-navigate="/profile"
    >
      <img src="${settings}" alt="Einstellungen" />
      <span>Einstellungen</span>
    </a>
  </div>
`;
